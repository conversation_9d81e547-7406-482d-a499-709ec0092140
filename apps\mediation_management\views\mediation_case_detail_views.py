#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : mediation_case_detail_views.py
<AUTHOR> JT_DA
@Date     : 2025/08/01
@File_Desc: 调解案件详情相关视图 - 微信小程序专用
"""

import logging
from rest_framework.generics import GenericAPIView
from rest_framework import serializers

from apps.mediation_management.models import MediationCase
from utils.permission_helper import WechatFaceAuthPermission
from utils.ajax_result import AjaxResult
from utils.expression_calculator import calculate_expression_with_asset_data

# 获取日志记录器
logger = logging.getLogger(__name__)


class MediationCaseDetailSerializer(serializers.Serializer):
    """调解案件详情序列化器 - 用于验证请求参数"""

    mediation_case_number = serializers.CharField(
        required=False, 
        max_length=50, 
        help_text="调解案件号，可选参数，用于额外验证案件信息"
    )

    def validate_mediation_case_number(self, value):
        """验证调解案件号是否存在"""
        if value is not None:
            try:
                MediationCase.objects.get(case_number=value)
            except MediationCase.DoesNotExist:
                raise serializers.ValidationError("指定的调解案件号不存在")
        return value


class MediationCaseDetailView(GenericAPIView):
    """
    调解案件详情获取视图 - 微信小程序专用

    提供微信小程序端获取调解案件详细信息的功能。
    该视图通过WechatFaceAuthPermission权限验证用户身份和人脸核身状态，
    基于调解案件ID获取案件的详细信息，包括处理后的调解信息和调解方案。
    """

    # 使用微信人脸核身权限认证
    permission_classes = [WechatFaceAuthPermission]
    
    serializer_class = MediationCaseDetailSerializer

    def get(self, request, id, *args, **kwargs):
        """
        获取调解案件详情信息

        通过WechatFaceAuthPermission权限验证用户身份和人脸核身状态，
        基于调解案件ID获取案件的详细信息，包括基本信息、调解信息和调解方案。

        **请求参数：**

        **路径参数：**
        - id (整数, 必需): 调解案件ID，通过URL路径参数传递

        **查询参数：**
        - mediation_case_number (字符串, 可选): 调解案件号，用于额外验证

        **请求数据示例：**
        ```
        GET /mediation_management/mediation_case/wechat/123/detail/
        GET /mediation_management/mediation_case/wechat/123/detail/?mediation_case_number=GZTJ20250801ABC123
        ```

        **响应数据结构：**

        成功响应：
        ```json
        {
            "code": 200,
            "msg": "获取成功",
            "state": "success",
            "data": {
                "mediation_case_number": "GZTJ20250801ABC123",
                "case_status": "in_progress",
                "case_status_cn": "进行中",
                "initiate_date": "2025-08-01",
                "close_date": null,
                "mediation_progress": "协议签署",
                "notarization_status": "not_notarized",
                "notarization_status_cn": "未公证",
                "mediation_info": [
                    {
                        "id": "GZTJ6dz1z75km",
                        "title": "应还金额",
                        "logic_type": "result_calculation",
                        "expression": "{债权总额}-{已还金额}",
                        "value": "150000.00"
                    }
                ],
                "mediation_plan": [
                    {
                        "id": "PLAN001",
                        "title": "分期还款方案",
                        "logic_type": "text_formatting",
                        "expression": "分{期数}期还款",
                        "value": "分12期还款"
                    }
                ],
                "repayment_note": "GZTJ20250801ABC123_张三_110101199001011234_华泰民商事调解中心"
            }
        }
        ```

        错误响应：
        ```json
        {
            "code": 404,
            "msg": "未找到对应的调解案件",
            "state": "fail"
        }
        ```
        """
        try:
            # 验证查询参数
            serializer = self.get_serializer(data=request.query_params)
            serializer.is_valid(raise_exception=True)
            
            mediation_case_number = serializer.validated_data.get('mediation_case_number')

            # 记录请求日志
            logger.info(f"开始处理调解案件详情获取，案件ID: {id}")

            # 获取调解案件对象
            try:
                mediation_case = MediationCase.objects.get(id=id)
            except MediationCase.DoesNotExist:
                logger.error(f"调解案件不存在，ID: {id}")
                return AjaxResult.not_found(msg="未找到对应的调解案件")

            # 如果提供了案件号，进行额外验证
            if mediation_case_number and mediation_case.case_number != mediation_case_number:
                logger.error(f"案件号不匹配，ID: {id}, 提供的案件号: {mediation_case_number}, 实际案件号: {mediation_case.case_number}")
                return AjaxResult.fail(msg="案件号与案件ID不匹配")

            # 构建响应数据
            response_data = {
                "mediation_case_number": mediation_case.case_number,
                "case_status": mediation_case.case_status,
                "case_status_cn": mediation_case.get_case_status_display(),
                "initiate_date": mediation_case.initiate_date.strftime("%Y-%m-%d") if mediation_case.initiate_date else None,
                "close_date": mediation_case.close_date.strftime("%Y-%m-%d") if mediation_case.close_date else None,
                "mediation_progress": self._get_mediation_progress(mediation_case),
                "notarization_status": mediation_case.notarization_status,
                "notarization_status_cn": mediation_case.get_notarization_status_display(),
                "mediation_info": self._process_mediation_info(mediation_case),
                "mediation_plan": self._process_mediation_plan(mediation_case),
                "repayment_note": self._generate_repayment_note(mediation_case)
            }

            logger.info(f"调解案件详情获取成功，案件ID: {id}")
            return AjaxResult.success(msg="获取成功", data=response_data)

        except Exception as e:
            logger.error(f"获取调解案件详情失败，案件ID: {id}, 错误: {str(e)}")
            return AjaxResult.fail(msg=f"获取失败：{str(e)}")

    def _get_mediation_progress(self, mediation_case):
        """
        获取调解进度信息

        根据案件状态和mediation_plan字段确定调解进度：
        - pending_confirm: 调解确认
        - in_progress + mediation_plan为空: 方案确认
        - in_progress + mediation_plan不为空: 协议签署
        - completed: 完成
        - 其他状态: 未知状态
        """
        if mediation_case.case_status == "pending_confirm":
            return "调解确认"
        elif mediation_case.case_status == "in_progress":
            if mediation_case.mediation_plan is None:
                return "方案确认"
            else:
                return "协议签署"
        elif mediation_case.case_status == "completed":
            return "完成"
        else:
            return "未知状态"

    def _process_mediation_info(self, mediation_case):
        """
        处理调解信息配置
        
        优先使用confirmed_mediation_config字段的快照数据，
        如果不存在则返回空列表
        """
        if not mediation_case.confirmed_mediation_config:
            logger.info(f"调解案件 {mediation_case.id} 没有确认的调解信息配置")
            return []

        try:
            mediation_config = mediation_case.confirmed_mediation_config
            processed_config = []

            for config_item in mediation_config:
                # 复制原始配置对象
                processed_item = config_item.copy()

                # 检查配置对象是否包含必要字段
                if not isinstance(config_item, dict):
                    logger.warning(f"跳过非字典格式的配置项: {config_item}")
                    processed_config.append(processed_item)
                    continue

                logic_type = config_item.get("logic_type")
                expression = config_item.get("expression")

                # 如果没有logic_type或expression，直接添加到结果中
                if not logic_type or not expression:
                    logger.warning(f"配置项缺少logic_type或expression字段: {config_item}")
                    processed_item["value"] = config_item.get("value", "")
                    processed_config.append(processed_item)
                    continue

                # 使用表达式计算器处理表达式
                if mediation_case.asset_package and mediation_case.asset_package_row_number:
                    calc_result = calculate_expression_with_asset_data(
                        mediation_case.asset_package,
                        mediation_case.asset_package_row_number,
                        expression,
                        logic_type,
                    )
                    if calc_result.get("success"):
                        processed_item["value"] = calc_result.get("result", "")
                    else:
                        logger.warning(f"表达式计算失败: {calc_result.get('error', '')}")
                        processed_item["value"] = config_item.get("value", "")
                else:
                    logger.warning("缺少资产包或行号信息，无法计算表达式")
                    processed_item["value"] = config_item.get("value", "")

                processed_config.append(processed_item)

            return processed_config

        except Exception as e:
            logger.error(f"处理调解信息配置失败: {str(e)}")
            return []

    def _process_mediation_plan(self, mediation_case):
        """
        处理调解方案配置
        
        优先使用confirmed_plan_config字段的快照数据，
        如果不存在则返回空列表
        """
        if not mediation_case.confirmed_plan_config:
            logger.info(f"调解案件 {mediation_case.id} 没有确认的调解方案配置")
            return []

        try:
            plan_config = mediation_case.confirmed_plan_config
            processed_config = []

            for config_item in plan_config:
                # 复制原始配置对象
                processed_item = config_item.copy()

                # 检查配置对象是否包含必要字段
                if not isinstance(config_item, dict):
                    logger.warning(f"跳过非字典格式的配置项: {config_item}")
                    processed_config.append(processed_item)
                    continue

                logic_type = config_item.get("logic_type")
                expression = config_item.get("expression")

                # 如果没有logic_type或expression，直接添加到结果中
                if not logic_type or not expression:
                    logger.warning(f"配置项缺少logic_type或expression字段: {config_item}")
                    processed_item["value"] = config_item.get("value", "")
                    processed_config.append(processed_item)
                    continue

                # 使用表达式计算器处理表达式
                if mediation_case.asset_package and mediation_case.asset_package_row_number:
                    calc_result = calculate_expression_with_asset_data(
                        mediation_case.asset_package,
                        mediation_case.asset_package_row_number,
                        expression,
                        logic_type,
                    )
                    if calc_result.get("success"):
                        processed_item["value"] = calc_result.get("result", "")
                    else:
                        logger.warning(f"表达式计算失败: {calc_result.get('error', '')}")
                        processed_item["value"] = config_item.get("value", "")
                else:
                    logger.warning("缺少资产包或行号信息，无法计算表达式")
                    processed_item["value"] = config_item.get("value", "")

                processed_config.append(processed_item)

            return processed_config

        except Exception as e:
            logger.error(f"处理调解方案配置失败: {str(e)}")
            return []

    def _generate_repayment_note(self, mediation_case):
        """
        生成还款备注信息
        
        格式：{调解案件号}_{债务人姓名}_{债务人身份证号}_华泰民商事调解中心
        """
        try:
            case_number = mediation_case.case_number or ""
            debtor_name = mediation_case.debtor.debtor_name if mediation_case.debtor else ""
            debtor_id_number = mediation_case.debtor.id_number if mediation_case.debtor else ""
            
            repayment_note = f"{case_number}_{debtor_name}_{debtor_id_number}_华泰民商事调解中心"
            
            logger.info(f"生成还款备注信息: {repayment_note}")
            return repayment_note
            
        except Exception as e:
            logger.error(f"生成还款备注信息失败: {str(e)}")
            return f"{mediation_case.case_number or 'UNKNOWN'}_华泰民商事调解中心"
